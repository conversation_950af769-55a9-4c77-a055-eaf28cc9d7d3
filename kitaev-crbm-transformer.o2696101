Job start at: Wed Sep  3 19:17:27 +08 2025
Running on node: hpc-pinaki-gpu2
GPU Information:
Wed Sep  3 19:17:27 2025       
+-----------------------------------------------------------------------------------------+
| NVIDIA-SMI 580.65.06              Driver Version: 580.65.06      CUDA Version: 13.0     |
+-----------------------------------------+------------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id          Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |           Memory-Usage | GPU-Util  Compute M. |
|                                         |                        |               MIG M. |
|=========================================+========================+======================|
|   0  NVIDIA H200 NVL                On  |   00000001:D6:00.0 Off |                    0 |
| N/A   35C    P0             73W /  600W |       0MiB / 143771MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+

+-----------------------------------------------------------------------------------------+
| Processes:                                                                              |
|  GPU   GI   CI              PID   Type   Process name                        GPU Memory |
|        ID   ID                                                               Usage      |
|=========================================================================================|
|  No running processes found                                                             |
+-----------------------------------------------------------------------------------------+
Loading modules...
Anaconda 2025 python3 module loaded.

Please run the following commands (include the quote): eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"

Loading anaconda2025/2025
  Loading requirement: cuda/12.2
Using GPU device: 0
Python path: /home/<USER>/.conda/envs/netket/bin/python
Python version: Python 3.12.11
Current conda environment: netket
==================== cRBM-Transformer 混合架构训练 ====================
Starting cRBM-Transformer hybrid architecture training...
Checkpoint enabled with interval: 500
Using model: cRBM_Transformer
Experiment name: crbm_transformer_train
[2025-09-03 19:17:45,303][HYDRA] Launching 1 jobs locally
[2025-09-03 19:17:45,303][HYDRA] 	#0 : model=cRBM_Transformer system=kitaev training.experiment_name=crbm_transformer_train training.gpu_mesh_shape=[1,1] training.checkpoint.enable=True training.checkpoint.save_interval=500 training.checkpoint.keep_history=True
/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/jax/_src/numpy/lax_numpy.py:5555: ComplexWarning: Casting complex values to real discards the imaginary part
  out_array: Array = lax_internal._convert_element_type(
/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/jax/_src/numpy/lax_numpy.py:5555: ComplexWarning: Casting complex values to real discards the imaginary part
  out_array: Array = lax_internal._convert_element_type(
/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/jax/_src/numpy/lax_numpy.py:5555: ComplexWarning: Casting complex values to real discards the imaginary part
  out_array: Array = lax_internal._convert_element_type(
/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/jax/_src/numpy/lax_numpy.py:5555: ComplexWarning: Casting complex values to real discards the imaginary part
  out_array: Array = lax_internal._convert_element_type(
当前工作目录: /home/<USER>/Repositories/Kitaev_model
Hydra运行目录: /home/<USER>/Repositories/Kitaev_model/results/L4x4_K1.01.01.0_J0.0_h0.10.10.1_L0.0/2025-09-03/19-17_crbm_transformer_train/Job_0
✓ 晶格结构图已保存
✓ Hydra标准配置文件位于 .hydra/ 目录
[2025-09-03 19:18:22] ==================================================
[2025-09-03 19:18:22] cRBM_Transformer for Kitaev Model
[2025-09-03 19:18:22] ==================================================
[2025-09-03 19:18:22] System parameters:
[2025-09-03 19:18:22]   - System size: L=(4,4), N=32
[2025-09-03 19:18:22]   - Kitaev coupling: Kx=(1.0, 1.0, 1.0)
[2025-09-03 19:18:22]   - Heisenberg coupling: J=0.0
[2025-09-03 19:18:22]   - Magnetic field: h=(0.1, 0.1, 0.1)
[2025-09-03 19:18:22]   - Lambda parameter: 0.0
[2025-09-03 19:18:22]   - Reference energy: -6.396
[2025-09-03 19:18:22] --------------------------------------------------
[2025-09-03 19:18:22] Model parameters:
[2025-09-03 19:18:22]   - Use symmetries = True
[2025-09-03 19:18:22]   - Number of symmetries = 32
[2025-09-03 19:18:22]   - Total parameters = 275474
[2025-09-03 19:18:22] --------------------------------------------------
[2025-09-03 19:18:22] Training parameters:
[2025-09-03 19:18:22]   - Learning rate: 0.05
[2025-09-03 19:18:22]   - Total iterations: 6300
[2025-09-03 19:18:22]   - Annealing cycles: 6
[2025-09-03 19:18:22]   - Initial period: 100
[2025-09-03 19:18:22]   - Period multiplier: 2.0
[2025-09-03 19:18:22]   - Temperature range: 0.0-1.0
[2025-09-03 19:18:22]   - Samples: 6144
[2025-09-03 19:18:22]   - Discarded samples: 0
[2025-09-03 19:18:22]   - Chunk size: 1024
[2025-09-03 19:18:22]   - Diagonal shift: 0.08
[2025-09-03 19:18:22]   - Gradient clipping: 0.8
[2025-09-03 19:18:22]   - Checkpoint enabled: interval=500
[2025-09-03 19:18:22]   - Checkpoint directory: results/L4x4_K1.01.01.0_J0.0_h0.10.10.1_L0.0/2025-09-03/19-17_crbm_transformer_train/Job_0/checkpoints
[2025-09-03 19:18:22] --------------------------------------------------
[2025-09-03 19:18:22] Device status:
[2025-09-03 19:18:22]   - Devices model: NVIDIA H200 NVL
[2025-09-03 19:18:22]   - Number of devices: 1
[2025-09-03 19:18:22]   - Sharding: True
[2025-09-03 19:18:22] ============================================================
Automatic SR implementation choice:  NTK
Error executing job with overrides: ['model=cRBM_Transformer', 'system=kitaev', 'training.experiment_name=crbm_transformer_train', 'training.gpu_mesh_shape=[1,1]', 'training.checkpoint.enable=True', 'training.checkpoint.save_interval=500', 'training.checkpoint.keep_history=True']
Traceback (most recent call last):
  File "/home/<USER>/Repositories/Kitaev_model/src/train.py", line 103, in main
    runner.run()
  File "/home/<USER>/Repositories/Kitaev_model/src/runner.py", line 550, in run
    self._run_training(n_cycles, n_train)
  File "/home/<USER>/Repositories/Kitaev_model/src/runner.py", line 599, in _run_training
    vmc.run(n_iter=n_iter, energy_log=self.energy_log)
  File "/home/<USER>/Repositories/Kitaev_model/src/utils/FE_VMC_SR.py", line 141, in run
    self.advance(1)
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/driver/abstract_variational_driver.py", line 245, in advance
    for _ in self.iter(steps):
             ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/driver/abstract_variational_driver.py", line 230, in iter
    self._dp = self._forward_and_backward()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/utils/timing.py", line 292, in timed_function
    result = fun(*args, **kwargs)  # type: ignore[misc]
             ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/experimental/driver/vmc_sr.py", line 340, in _forward_and_backward
    local_energies = self.state.local_estimators(self._ham)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/utils/timing.py", line 292, in timed_function
    result = fun(*args, **kwargs)  # type: ignore[misc]
             ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/vqs/mc/mc_state/state.py", line 636, in local_estimators
    return local_estimators(self, op, chunk_size=chunk_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/vqs/mc/mc_state/state.py", line 821, in local_estimators
    return _local_estimators_kernel(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/vqs/mc/mc_state/state.py", line 795, in _local_estimators_kernel
    O_loc = kernel(apply_fun, variables, samples, extra_args)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/vqs/mc/kernels.py", line 219, in local_value_kernel_jax_chunked
    return local_value_chunked(σ)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/jax/_vmap_chunked.py", line 45, in _eval_fun_in_chunks_sharding
    return sharding_decorator(f, sharded_args_tree)(*args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/jax/sharding.py", line 462, in _fun
    res = _f(*args)
          ^^^^^^^^^
TypeError: 'NoneType' object is not iterable
--------------------
For simplicity, JAX has removed its internal frames from the traceback of the following exception. Set JAX_TRACEBACK_FILTERING=off to include these.

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.
cRBM-Transformer训练完成！混合架构模型已保存。
Job finished at: Wed Sep  3 19:19:29 +08 2025
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Resource Usage on 2025-09-03 19:19:29.314733:
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	JobId: 2696101.hpc-pbs-sched1
	Project: gs_spms_psengupta
	Exit Status: 0
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	NCPUs: Requested(4), Used(4)
	CPU Time Used: 00:00:06
	Memory: Requested(75gb), Used(127280kb)
	Vmem Used: 127280kb
	Walltime: Requested(1440:00:00), Used(00:02:08)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	Execution Nodes Used: (hpc-pinaki-gpu2:ngpus=1:ncpus=4:mem=78643200kb)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	No GPU-related information available for this job.
